import cv2
import numpy as np
from tqdm import tqdm

##检查opencv版本
cv_version=cv2.__version__
print(cv_version)
if cv_version!='3.4.2':
    raise ValueError( 'Opencv version incorrect!')

##求解角度的函数
def get_angle(i,j,R):
    if j>R:
        return np.arctan((i-R)/(j-R))+np.pi/2  
    elif j<R:
        return np.arctan((i-R)/(j-R))+np.pi/2*3
    elif i<R:
        return 0
    else:
        return np.pi


##载入任意一张展开图
image=cv2.imread('./images/0.jpg')
##计算mapx和mapy并保存
H,W=image.shape[:2]
R=W/np.pi/2
R=int(R)
mapx=np.zeros([2*R,2*R],dtype=np.float32)
mapy=np.zeros([2*R,2*R],dtype=np.float32)
for i in tqdm(range(mapx.shape[0])):
    for j in range(mapx.shape[1]):
        angle=get_angle(i,j,R)
        radius=np.linalg.norm(np.array([j,i])-np.array([R,R]),ord=2)
        if radius>R:continue
        mapx[i,j]=angle/np.pi/2*W
        mapy[i,j]=(1-radius/R)*H
np.save('./npy/mapx.npy',mapx)
np.save('./npy/mapy.npy',mapy)


##载入remap矩阵,并将展开图remap回鱼眼图
mapx=np.load('./npy/mapx.npy')
mapy=np.load('./npy/mapy.npy')
image=cv2.imread('./images/0.jpg')
image[0,0]=[0,0,0]#把左上角的点变为黑色，用于把半径外的点变为黑色
image_remap=cv2.remap(image, mapx, mapy, interpolation=cv2.INTER_LINEAR,borderMode=cv2.BORDER_CONSTANT)
cv2.imwrite('./images/1.jpg',image_remap)
