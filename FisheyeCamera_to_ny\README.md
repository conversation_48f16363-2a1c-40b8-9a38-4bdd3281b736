# 文件介绍

#### calibrate_1.py
棋盘标定法，运行后会对 ./inputs 文件夹中的所有图片进行棋盘标定（棋盘角点的检测结果会生成在 ./shows 中），并对 ./images/0.jpg 进行矫正，生成矫正图 ./images/1.jpg。

#### calibrate_2.py
横向展开法，运行后会对 ./images/0.jpg 进行变形，生成矫正图 ./images/2.jpg。

#### calibrate_3.py
经度展开法，运行后会对 ./images/0.jpg 进行变形，生成矫正图 ./images/3.jpg。

#### calibrate_4.py
纬度展开法，运行后会对 ./images/0.jpg 进行变形，生成矫正图 ./images/4.jpg。

#### calibrate_34.py
经度展开和纬度展开融合，运行后会对 ./images/0.jpg 进行变形，生成矫正图 ./images/34.jpg。
<br/>

#### 更多知识，欢迎登录<font color=#FF0000 >江大白</font>官方网站学习和讨论：www.jiangdabai.com