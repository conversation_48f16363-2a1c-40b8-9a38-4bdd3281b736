import cv2

img = cv2.imread('./images/2.jpg')
h, w = img.shape[:2]

# 计算中心点
center_x = w // 2
center_y = h // 2

# 计算正方形区域的左上角和右下角坐标
half_size = 1080 // 2
x1 = center_x - half_size
y1 = center_y - half_size
x2 = center_x + half_size
y2 = center_y + half_size

# 防止越界
x1 = max(0, x1)
y1 = max(0, y1)
x2 = min(w, x2)
y2 = min(h, y2)

# 裁剪
crop_img = img[y1:y2, x1:x2]

cv2.imwrite('./images/2_center_1080.jpg', crop_img)