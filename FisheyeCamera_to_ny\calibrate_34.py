import cv2
import numpy as np
from tqdm import tqdm

def get_useful_area(image):
    # Convert to grayscale and threshold
    image_gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
    _, image_binary = cv2.threshold(image_gray, 0, 255, cv2.THRESH_BINARY+cv2.THRESH_OTSU)
    
    # Find contours
    contours, _ = cv2.findContours(image_binary, cv2.RETR_TREE, cv2.CHAIN_APPROX_SIMPLE)
    if not contours:
        raise ValueError("No contours found in the image")
    
    # Get largest contour
    contour_fisheye = sorted(contours, key=cv2.contourArea, reverse=True)[0]
    center, radius = cv2.minEnclosingCircle(contour_fisheye)
    
    # Create circular mask
    mask = np.zeros_like(image, dtype=np.uint8)
    mask = cv2.circle(mask, (int(center[0]), int(center[1])), int(radius), (1,1,1), -1)
    
    # Apply mask and crop
    image_useful = image * mask
    y1 = max(0, int(center[1])-int(radius))
    y2 = min(image.shape[0], int(center[1])+int(radius))
    x1 = max(0, int(center[0])-int(radius))
    x2 = min(image.shape[1], int(center[0])+int(radius))
    
    image_fisheye = image_useful[y1:y2, x1:x2, :]
    
    if image_fisheye.size == 0:
        raise ValueError("Cropped image is empty - check center and radius calculations")
        
    return image_fisheye

image=cv2.imread('./images/p2.jpg')
image=get_useful_area(image)
mx = max(image.shape[0], image.shape[1])
image = cv2.resize(image, (mx, mx))
if image.shape[0]!=image.shape[1]:
    raise ValueError('Image width isn\'t equal to height!')

R=image.shape[0]//2
mapx_lon=np.zeros([2*R,2*R],dtype=np.float32)
mapy_lon=np.zeros([2*R,2*R],dtype=np.float32)
mapx_lat=np.zeros([2*R,2*R],dtype=np.float32)
mapy_lat=np.zeros([2*R,2*R],dtype=np.float32)

# 经度矫正
for i in tqdm(range(mapx_lon.shape[0])):
    for j in range(mapx_lon.shape[1]):
        mapx_lon[i,j]=(j-R)/R*(R**2-(i-R)**2)**0.5+R
        mapy_lon[i,j]=i

# 纬度矫正
for i in tqdm(range(mapx_lat.shape[0])):
    for j in range(mapx_lat.shape[1]):
        mapx_lat[i,j]=j
        mapy_lat[i,j]=(i-R)/R*(R**2-(j-R)**2)**0.5+R

# 融合（加权平均，权重可调）
alpha = 0.8
mapx = alpha * mapx_lon + (1-alpha) * mapx_lat
mapy = alpha * mapy_lon + (1-alpha) * mapy_lat

np.save('./npy/mapx_mix.npy', mapx)
np.save('./npy/mapy_mix.npy', mapy)

# remap
image=cv2.imread('./images/p2.jpg')
image=get_useful_area(image)
image_remap=cv2.remap(image, mapx, mapy, interpolation=cv2.INTER_LINEAR, borderMode=cv2.BORDER_CONSTANT)
cv2.imwrite('./images/pp2.jpg', image_remap)
